should = require('should')
helpers = require('../00_common/helpers')
MSG_STRINGS = helpers.MSG_STRINGS

NO_REAL_GEOCODING = false
request = null

propAddress = require('../../built/lib/propAddress')
{unifyAddress} = require('../../built/lib/helpers_string')

# TORUN: `
# coffee ./setup.coffee
# ./mate4.sh compile
# ./unitTest/test.sh -f lib/propAddress.js
#`

describe 'Property Address Process',->
  before (done) ->
    @timeout(300000)
    # request = helpers.getServer()
    done()
    #insert one record to each collection. import record. read out record from collection. compare
    #
    # helpers.loginAndThen request, 'admin',->
    #   setTimeout(done, 2500)
    # return

  describe 'separateUnitFromAddr ', ->
    # @timeout(30000)
    tests=[
      {
        input: '1-123 abc street',
        unt: '1',
        addr: '123 Abc St'
      },
      {
        input: '1-1-123 abc street',
        unt: '1-1',
        addr: '123 Abc St'
      },
      {
        input: 'PH-12-123 abc street',
        unt: 'Ph-12',
        addr: '123 Abc St'
      },
      {# TO COMFIRM
        input:'LT 20 main st',
        addr:'Lt 20 Main St'
      },
      {
        input:'LT 2A 20 main street',
        unt :'Lot 2a',
        addr:'20 Main St'
      },
      {
        input:'LT-2A 20 main street',
        unt :'Lot-2a',
        addr:'20 Main St'
      },
      {
        input: 'u 1 123 abc street',
        unt :'1',
        addr:'123 Abc St'
      },
      {
        input: 'unit 1 123 abc street',
        unt : '1',
        addr: '123 Abc St'
      },
      {
        input: '#1 123 abc street',
        unt : '1',
        addr: '123 Abc St'
      },
      {
        input: '1 123 abc street',
        unt : '1',
        addr: '123 Abc St'
      },
      {
        input:'PH 12 123 main st',
        unt :'Ph 12',
        addr:'123 Main St'
      },
      {
        input:'123 main st. unit#7260',
        unt :'7260',
        addr:'123 Main St'
      },
      {
        input:'123 main st. unit# 7260',
        unt :'7260',
        addr:'123 Main St'
      },
      { # TO COMFIRM
        input:'Lot 199 212 Crestview Dr',
        addr:'Lot 199 212 Crestview Dr'
      },
      {
        input:'Lot 11 Gord Vinson Ave',
        addr:'Lot 11 Gord Vinson Ave'
      },
      {
        input:'560 Uxbridge -pickering Line',
        addr:'560 Uxbridge -pickering Line'
      },
      {
        input:'85 Rue Alice-Parizeau',
        addr:'85 Rue Alice-Parizeau'
      },
      {
        input:'3876 Muskoka 118 Road Unit# Carling 10-W2',
        unt:'Carling 10-W2',
        addr:'3876 Muskoka 118 Rd'
      },
      {
        input:'47479 Rge Rd 31 302',
        unt:'302',
        addr:'47479 Rge Rd 31'
      },
      {
        input:'47479 Rge Rd Rd',
        addr:'47479 Rge Rd'
      },
      {
        input:'3942 Squilax-Anglemont Hwy',
        addr:'3942 Squilax-Anglemont Hwy'
      },
      {
        input:'10 2520 10 Ave Se',
        unt:'10',
        addr:'2520 10 Ave SE'
      },
      {
        input:'3876 Muskoka 118 Road Unit# Carling 2 - W4',
        unt:'Carling 2 - W4',
        addr:'3876 Muskoka 118 Rd'
      },
      {
        input:'#215 1765 Leckie Road,',
        unt:'215',
        addr:'1765 Leckie Rd'
      },
      {
        input:'153070 Twp Rd 1-4',
        unt:'4',
        addr:'153070 Twp Rd 1'
      },
      {
        input:' HWY-3',
        addr:'Hwy-3'
      },
      {
        input:'9640 92 Avenue Unit# 8-',
        unt:'8',
        addr:'9640 92 Ave',
      },
      {
        input:'5289 HWY 7',
        addr:'5289 Hwy 7'
      },
      {
        input: '123 19 STREET.',
        addr:'123 19 St'
      },
      {
        input: '123 19 STREET. SE',
        addr:'123 19 St SE'
      },
      {
        input:'#280, 23 Sunpark Drive SE',
        unt:'280',
        addr:'23 Sunpark Dr SE'
      },
      {
        input:'2 Quail Hollow',
        addr:'2 Quail Hollow'
      },
      {
        input:'4630 Dufferin Street, Suite 301',
        addr:'4630 Dufferin St',
        unt:'301'
      },
      {
        inut:'1231313',
        addr:null,
        unt:null
      },
      {
        input:'2480 15th Sideroad,'
        addr:'2480 15th Sideroad',
        unt:null
      },
      {
        input:'30 Indianola Crescent #penthouse'
        addr:'30 Indianola Cres',
        unt:'Ph'
      },
      {
        input:'150 Prospect Street Unit  Penthouse'
        addr:'150 Prospect St',
        unt:'Ph'
      },
      {
        input:'246 Scott Ave.,lower Unt'
        addr:'246 Scott Ave',
        unt:'Lower'
      },
      {
        input:'Lot 235 Paradise Lane E Fr#866'
        addr:'Lot 235 Paradise Lane E Fr',
        unt:'866'
      },
      {
        input:'7260 12 St Se'
        addr:'7260 12 St SE',
        unt:null
      },
      {
        input:'7890 12th Street, S.e.'
        addr:'7890 12th St SE',
        unt:null
      },
      {
        input: '4400 Fourth Ave'
        addr: '4400 4th Ave',
        unt:null
      },
      {
        input:'0 Leeward Penthouse',
        addr:'0 Leeward Penthouse',
        unt:null
      },
      {
        input:'10 Brandy Lane Dr',
        addr:'10 Brandy Lane Dr'
      },
      {
        input:'1050 18th Street Northeast '
        addr:'1050 18th St NE'
      },
      {
        input:'11 Mann Avenue North '
        addr:'11 Mann Ave N'
      },
      {
        input:'121 King Street West',
        addr:'121 King St W'
      },
      {
        input:'171 St. James Street West ',
        addr:'171 St. James St W'
      },
      {
        input:'3421 West River East Side Rd',
        addr:'3421 West River East Side Rd'
      },
      {
        input:'112 106 Avenue',
        addr:'112 106 Ave'
      },
      {
        input:'374 CR-2',
        addr:'374 Cr-2'
      },
      {
        input:'2580 RR-23',
        addr:'2580 Rr-23'
      },
      {
        input:'7 HWY-7'
        addr:'7 Hwy-7'
      },
      # 34-10046 Township Road 422
      #
      {
        input:'34-10046 Township Road 422'
        addr:'10046 Township Rd 422'
        unt:'34'
      },
      {
        input:'#14 TOWNSHIP ROAD 422'
        addr:'Township Rd 422'
        unt: '14'
      },
      {
        input:'11311 25 REGIONAL RD',
        addr:'11311 25 Regional Rd'
      },
      {
        input:'320 ROAD AVE E',
        addr:'320 Road Ave E' # may need to processed to RD?
      },
      {
        input:'8 The Esplanade',
        addr:'8 The Esplanade'
      },
      {
        input:'23354 Township Rd 523',
        addr:'23354 Township Rd 523'
      },
      {
        input:'45 Hwy 7',
        addr:'45 Hwy 7'
      },
      {
        input:'113-10046 township rd 422'
        addr:'10046 Township Rd 422',
        unt:'113'
      },
      {
        input:'2090 Wyandotte Street East Unit# 200 & 201',
        addr:'2090 Wyandotte St E',
        unt:'200 & 201'
      },
      {
        input:'380 PELISSIER Unit# 210'
        addr:'380 Pelissier',
        unt:'210'
      },
      {
        input:'50 King St # Bay 1',
        addr:'50 King St Bay 1',
      },
      {
        input:'2835 Sheffield Place Unit# 19'
        addr:'2835 Sheffield Pl',
        unt:'19'
      },
      {
        input:'Lot 1 - Dl9008 Highway 95',
        addr:'Dl9008 Hwy 95',
        unt:'Lot 1'
      },
      {
        input:'271 Mill Street Unit# B2',
        addr:'271 Mill St',
        unt:'B2'
      },
      {
        input:'2093 Fairview St, 402',
        addr:'2093 Fairview St',
        unt:'402'
      },
      {
        input:'Pt Lot County Rd 15',
        addr:'Pt Lot County Rd 15'
      },
      {
        input:'320 Croft Unit# 1-3',
        addr:'320 Croft',
        unt:'1-3'
      },
      {
        input:'9158 Highway 28',
        addr:'9158 Hwy 28'
      },
      {
        input: '507 22315 122 Avenue',
        addr: '22315 122 Ave'
        unt: '507'
      },
      {
        input:'63 /63A Main st'
        addr:'63 /63a Main St'
      },
      {
        input:'#111 107 Village Centre Court',
        addr:'107 Village Centre Crt',
        unt:'111'
      }
      {
        input:'#89 - 8 TOWNWOOD DR'
        addr:'8 Townwood Dr'
        unt:'89'
      }
      {
        input: '#186 - 1951 RATHBURN RD E',
        addr: '1951 Rathburn Rd E'
        unt:'186'
      }
      {
        input: '10019 106 AV NW'
        addr: '10019 106 Ave NW'
      }
      {
        input: '796375 19 GREY RD'
        addr:'19 Grey Rd'
        unt: '796375'
      }
      {
        input: '#104 - 1050 SHAWNMARR RD',
        addr:'1050 Shawnmarr Rd'
        unt:'104'
      }
      {
        input: '# 15 - 261 ROSE ST'
        addr:'261 Rose St'
        unt: '15'
      }
      {
        input:'324 Avenue André-Chartrand', # only Avenue in last will be format
        addr:'324 Avenue André-Chartrand'
      }
      {
        input:'324 Av. André-Chartrand'
        addr:'324 Av. André-Chartrand'
      }
      {
        input: '5 A.V. Nolan Dr'
        addr: '5 A.V. Nolan Dr' # no camelcase for A.V.
      }
      {
        input: '6 AV. Nolan Dr'
        addr: '6 Av. Nolan Dr' # only remove . for last one now
      }
      {
        input:'5 Avenue Bay'
        addr:'5 Avenue Bay'
      }
      {
        input: '1442 RAVENSCROFT AV SE',
        addr:'1442 Ravenscroft Ave SE'
      },
      {
        input:'120 Victoria St S Street S Unit# 2102',
        addr:'120 Victoria St S St S'
        unt: '2102'
      },
      {
        input:'331 Sheddon Avenue 138',
        addr:'331 Sheddon Ave'
        unt: '138'
      },
      {
        input:'50 Pinery(basement) Lane',
        addr:'50 Pinery Lane'
        unt: null
      },
      {
        input:'304 1234 Avenue Rd',
        addr:'1234 Avenue Rd'
        unt: '304'
      },
      {
        input:'210 G4 4653 Blackcomb Way'
        addr:'4653 Blackcomb Way'
        unt:'210 G4'
      },
      {
        input:'407 C&h 187 Kananaskis Way'
        addr:'187 Kananaskis Way'
        unt:'407 C&h'
      },
      {
        input:'202/201 A 366 Clubhouse Dr'
        addr:'366 Clubhouse Dr'
        unt:'202/201 A'
      },
      {
        input:'411 & 412 4885 Kingsway'
        addr:'4885 Kingsway'
        unt:'411&412'
      },
      {
        input:'#720 -25 COLE ST'
        addr:'25 Cole St'
        unt:'720'
        origUnt:'720'
      },
      {
        input:'#720 - 25 COLE ST'
        addr:'25 Cole St'
        unt:'720'
        origUnt:'720'
      },
      {
        input:'#720-25 COLE ST'
        addr:'25 Cole St'
        unt:'720'
        origUnt:'720'
      },
      {
        input:'8282 Highway 37 Rd N'
        addr:'8282 Highway 37 Rd N'
        unt:null
      },
      {
        input:'47 Fairview Ave Ave E'
        addr:'47 Fairview Ave E'
        unt:'(upper)'
        origUnt:'(upper)'
      },
      {
        input:'309-220 KENYON STREET W UNIT#309'
        addr:'220 Kenyon St W'
        unt:'309'
        origUnt:'309'
      },
      {
        input:'100 Dufay Rd'
        addr:'100 Dufay Rd'
        unt:'10'
        origUnt:'10'
      },
      {
        input:'1 Dufay Rd'
        addr:'1 Dufay Rd'
        unt:'1'
        origUnt:'1'
      },
      {
        input:'#BDRM #3 -192 SUNDEW DR'
        addr:'192 Sundew Dr'
        origUnt:'Bdrm #3'
        unt:'Bdrm 3'
      },
      {
        input:'36 Champlain Cres Building #1',
        addr:'36 Champlain Cres Building #1'
        origUnt:'32'
        unt:'32'
      },
      {
        # 当前input解析不出unit，采用原有unit，addr
        input:'PC-38 45 Pearlgarden Close'
        addr:'Pc-38 45 Pearlgarden Close'
        origUnt:'38'
        unt:'38'
      },
      {
        input:'#ROOM #3 -3 - 91 WESTMOUNT RD N'
        addr:'3 - 91 Westmount Rd N'
        origUnt:'Room #3'
        unt:'Room 3'
      },
      {
        input:'#ROOM#3 -3 - 91 WESTMOUNT RD N'
        addr:'3 91 Westmount Rd N'
        unt:'Room3'
      },
      {
        input:'#ROOM A -2266 THORNTON RD N'
        addr:'2266 Thornton Rd N'
        origUnt:'Room A'
        unt:'Room A'
      },
      {
        input:'#904-904 155 YORKVILLE AVE'
        addr:'155 Yorkville Ave'
        unt:'904'
      },
      {
        input:'#1-4 13635 34 ST NW'
        addr:'13635 34 St NW'
        unt:'1-4'
      },
      {
        input:'#103 -796468 19 GREY ROAD RD'
        addr:'796468 19 Grey Rd'
        origUnt:'103'
        unt:'103'
      },
      {
        input:'#1-13635 34 ST NW'
        addr:'13635 34 St NW'
        unt:'1'
      },
      {
        input:'#2-416 4245 139 AV NW'
        addr:'4245 139 Ave NW'
        unt:'2-416'
      },
      {
        input:'712 Rossland Rd E	'
        addr:'712 Rossland Rd E'
        origUnt:'712'
        unt:'712'
      },
      {
        input:'4 Heritage Way, Unit 204'
        addr:'4 Heritage Way'
        origUnt:'4'
        unt:'204'
      },
      {
        input:'105 Rita Crescent  Lower Unit'
        addr:'105 Rita Cres'
        origUnt:'C'
        unt:'Lower'
      },
      {
        input:'1-140 King Street E'
        addr:'140 King St E'
        origUnt:'1'
        unt:'1'
      },
      {
        input:'1-140 King Street E'
        addr:'140 King St E'
        origUnt:1
        unt:'1'
      },
      {
        input:'1-140 King Street E'
        addr:'140 King St E'
        unt:'1'
      },
      {
        # 3050 ORLEANS Road|Unit #103
        input:'3460 South Millway|Unit #23'
        addr:'3460 South Millway'
        unt:'23'
      },
      {
        # 3050 ORLEANS Road|Unit #103
        input:'3460 South Millway|Unit #23'
        addr:'3460 South Millway'
        origUnt: 'Unit #23'
        unt:'23'
      },
      {
        # DDF26511274
        input:'#106 -1055 FORESTWOOD DR'
        addr:'1055 Forestwood Dr'
        # addr: '#106 -1055 FORESTWOOD DR',
        # uaddr: 'CA:ON:MISSISSAUGA:#106 -1055 FORESTWOOD DR',
        unt:'106'
      },
      {
        input:'#11 -86 JOYMAR DR',
        addr:'86 Joymar Dr'
        origUnt:'11'
        unt:'11'
      },
      {
        input:'LOT 55 BROOKLYN Avenue',
        addr:'Brooklyn Ave',
        origUnt:'LOT 55',
        unt:'Lot 55'
      },
      {
        input:'LOT 55 BROOKLYN Avenue',
        addr:'Lot 55 Brooklyn Ave',
      },
      {
        input:'LOT 2020-2 FOXLAND Court',
        addr:'Lot 2020-2 Foxland Crt'
      },
      {
        input:null
      },
      {
        input:{},
        addr:'[object Object]'
      },
      {
        input:604,
        addr:'604'
      }
      # TODO: origAddr: 'Lot 1 6422 Highway 4',

      # 380 PELISSIER Unit# 210
      # 50 King St # Bay 1
      # 2835 Sheffield Place Unit# 19
      # Lot 1 - Dl9008 Highway 95
      # 271 Mill Street Unit# B2
      # 2093 Fairview St, 402
      # {
      #   # ignore this case.
      #   input:'2808 Muskoka Rd 169'
      # }
      # {
      #   input:'#2ND FLR -16 DURHAM ST',
      #   addr:'112 106 Ave'
      # }
      # {
      #   input:'[8500 - 8799] Boultbee Rd, LOT 9'
      # }
    ]

      # "2480 15th Sideroad",
      # "2480 15th Sideroad, P.o. Box" ->'PO BOX'
      
      # "4th Ave",
      # "4400 Fourth Ave"
      #LOTS#1,2,3 BLUE HERON Crescent



    tests.forEach (test)->
      it "should parse #{test.input} to be unt: #{test.unt}, addr:#{test.addr} ", (done)->
        if test.origUnt
          {unt,addr,cases} = propAddress.separateUnitFromAddr(test.input,test.origUnt)
        else
          {unt,addr,cases} = propAddress.separateUnitFromAddr(test.input)
        # console.log 'unt->',unt,' addr->',addr,' cases->',cases,showAddr
        if test.unt
          should.exists(unt)
          unt.should.equal(test.unt)
        else #should not has parsed unt
          should.not.exists(unt)
        if test.addr
          should.exists(addr)
          addr.should.equal(test.addr)
        else
          should.not.exists(addr)
        done()

    # filterZip
  describe 'filterZip ', ->
    tests=[
      {
        input: 123456,
        zip: 123456
      },
      {
        input:'N8W5K5',
        zip:'N8W5K5'
      },
      {
        input:'t2s2g5',
        zip:'T2S2G5'
      },
      {
        input:'03025',
        zip:'03025'
      },
      {
        input:'L9W-2R7',
        zip:'L9W2R7'
      },
      {
        input:'------',
        zip:'------'
      }
      {
        input:'L9W,2R7',
        zip:'L9W2R7'
      }
      {
        input:'M4P,M4',
        zip:'M4PM4'
      }
      {
        input:'L47 1n8', #not ca address
        zip:'L47 1N8'
      }
      {
        input:'',
        zip:null
      }
    ]
    tests.forEach (test)->
      it "should format #{test.input} to be zip: #{test.zip}", (done)->
        zip = propAddress.filterZip(test.input)
        # console.log 'input',test.input,'zip',test.zip
        if test.zip
          zip.should.equal(test.zip)
        else
          should.not.exists(zip)
        done()
    
    #formatProvAndCity
  describe 'formatProvAndCity ', ->
    tests = [
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'MARKHAM',
          addr:'#2-7400 MARKHAM ROAD'
          zip:'L4b 1n8'
          unt:'#2'
          ptype2:['Apartment']
        }
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Markham',
          addr:'7400 Markham Rd'
          faddr: '7400 Markham Rd, Markham, ON L4B1N8, CA'
          zip:'L4B1N8'
          unt:'2'
          origUnt: '#2'
          st_num:'7400'
          st:'Markham'
          st_sfx:'Rd'
          showAddr:'7400 MARKHAM ROAD'
          ptype2:['Apartment']
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'MARKHAM',
          st_num:'#2-7400'
          st:'MARKHAM ROAD'
          zip:'L4b 1n8'
          cmty:'Middlefield',
          ptype2:['Condo']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Markham',
          addr:'7400 Markham Rd'
          faddr: '7400 Markham Rd, Markham, ON L4B1N8, CA'
          zip:'L4B1N8'
          unt:'2'
          st:'Markham'
          st_num:'7400'
          st_sfx:'Rd'
          showAddr:'7400 MARKHAM ROAD'
          cmty:'Middlefield'
          ptype2:['Condo']
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'MARKHAM',
          st_num:'#2-7401'
          st:'MARKHAM ROAD'
          zip:'L4b 1n8'
          cmty:'Middlefield',
          st_dir:'W'
          ptype2:['Condo']
        },
        output:{
          cnty:'CA',
          dir: 'W',
          prov:'ON',
          city:'Markham',
          addr:'7401 Markham Rd W'
          faddr: '7401 Markham Rd W, Markham, ON L4B1N8, CA'
          zip:'L4B1N8'
          unt:'2'
          st:'Markham'
          st_dir: 'W'
          st_num:'7401'
          st_sfx:'Rd'
          showAddr:'7401 MARKHAM ROAD W'
          cmty:'Middlefield'
          ptype2:['Condo']
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'MARKHAM',
          addr:'Lot 20 MARKHAM ROAD'
          zip:'L4b 1n8'
          unt:'#2'
          ptype2:['Townhouse']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Markham',
          addr:'Lot 20 Markham Rd'
          faddr: 'Lot 20 Markham Rd, Markham, ON L4B1N8, CA'
          zip:'L4B1N8'
          unt:'2'
          origUnt:'#2'
          st_num:'Lot 20'
          st:'Markham'
          st_sfx: 'Rd'
          showAddr:'Lot 20 MARKHAM ROAD'
          ptype2:['Townhouse']
          saletp:['Lease']
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'North york',
          addr:'238 Doris Ave'
          ptype2:['Condo']
          saletp:['Sale']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Toronto',
          addr:'238 Doris Ave'
          faddr: '238 Doris Ave, Toronto, ON, CA'
          st_num:'238'
          st:'Doris'
          st_sfx: 'Ave'
          showAddr:'238 Doris Ave'
          ptype2:['Condo']
          saletp:['Sale'],
          subCity:'North York'
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'Oakville',
          addr:'331 Sheddon Avenue 138'
          ptype2:['Apartment']
          saletp:['Sale']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Oakville',
          addr:'331 Sheddon Ave'
          faddr: '331 Sheddon Ave, Oakville, ON, CA'
          st_num:'331'
          st:'Sheddon'
          st_sfx: 'Ave'
          showAddr:'331 Sheddon Avenue'
          unt:'138'
          ptype2:['Apartment']
          saletp:['Sale']
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'ONTARIO',
          city:'Oakville',
          addr:'331 Sheddon Avenue|138'
          ptype2:['Apartment']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Oakville',
          addr:'331 Sheddon Ave'
          faddr: '331 Sheddon Ave, Oakville, ON, CA'
          st_num:'331'
          st:'Sheddon'
          st_sfx: 'Ave'
          showAddr:'331 Sheddon Avenue',
          unt: '138'
          ptype2:['Apartment']
          saletp:['Lease']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'NS',
          city:'Halifax',
          addr:'301 2842-2856 Gottingen St'
          ptype2:['Townhouse']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'NS',
          city:'Halifax',
          addr:'2842-2856 Gottingen St',
          unt:'301',
          st_num:'2842-2856',
          st:'Gottingen',
          st_sfx:'St',
          showAddr:'2842-2856 Gottingen St'
          faddr:'2842-2856 Gottingen St, Halifax, NS, CA'
          ptype2:['Townhouse']
          saletp:['Lease']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Langley',
          addr:'20282 72b Ave'
          ptype2:['Condo']
        },
        output:{
          cnty:'CA',
          prov:'BC',
          city:'Langley',
          addr:'20282 72b Ave',
          st_num:'20282',
          st:'72b',
          st_sfx:'Ave',
          showAddr:'20282 72b Ave',
          faddr:'20282 72b Ave, Langley, BC, CA'
          ptype2:['Condo']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Langley',
          addr:'20282 72b Ave'
          ptype2:['Townhouse']
          saletp: [ 'Sale' ]
        },
        output:{
          cnty: 'CA',
          prov: 'BC',
          city: 'Langley',
          addr: '20282 72b Ave',
          ptype2: [ 'Townhouse' ],
          saletp: [ 'Sale' ],
          showAddr:'20282 72b Ave',
          st: '72b',
          st_num:'20282',
          st_sfx:'Ave'
          faddr: '20282 72b Ave, Langley, BC, CA'
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'ON',
          city:'Toronto',
          addr:'#lph4803 -50 Charles St E'
          ptype2:['Apartment']
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Toronto',
          addr:'50 Charles St E',
          unt:'Lph4803',
          st_num:'50',
          st:'Charles',
          st_sfx:'St',
          dir:'E',
          st_dir:'E',
          showAddr:'50 Charles St E',
          faddr:'50 Charles St E, Toronto, ON, CA'
          ptype2:['Apartment']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Victoria',
          addr:'A301[aug]-810 Humboldt St'
          ptype2:['Townhouse']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'BC',
          city:'Victoria',
          addr:'810 Humboldt St',
          unt:'A301 aug',
          st_num:'810',
          st:'Humboldt',
          st_sfx:'St',
          showAddr:'810 Humboldt St'
          faddr:'810 Humboldt St, Victoria, BC, CA'
          ptype2:['Townhouse']
          saletp:['Lease']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'ON',
          city:'Cambridge',
          addr:'404&410 Dundas St S'
          st:'Dundas'
        },
        # 没有st,st_num,unit
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Cambridge',
          addr:'404&410 Dundas St S',
          st:'Dundas',
          dir:'S',
          st_dir:'S',
          st_sfx:'St',
          showAddr:'404&410 Dundas St S',
          faddr:'404&410 Dundas St S, Cambridge, ON, CA'
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Golden',
          addr:'204 (a&b) - 1549 Kicking Horse'
          ptype2:['Townhouse']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'BC',
          city:'Golden',
          addr:'1549 Kicking Horse',
          unt:'204 (a&b)',
          st_num:'1549',
          st:'Kicking Horse',
          showAddr:'1549 Kicking Horse'
          faddr:'1549 Kicking Horse, Golden, BC, CA'
          ptype2:['Townhouse']
          saletp:['Lease']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'ON',
          city:'Toronto',
          addr:'Ph817 Spadina Ave'
          ptype2:['Detached']
          saletp:['Sale']
          st:'Spadina'
        },
        output:{
          cnty:'CA',
          prov:'ON',
          city:'Toronto',
          addr:'Ph817 Spadina Ave',
          st:'Spadina',
          showAddr:'Ph817 Spadina Ave',
          faddr:'Ph817 Spadina Ave, Toronto, ON, CA'
          ptype2:['Detached']
          saletp:['Sale'],
          st_sfx:'Ave'
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'AB',
          city:'Calgary',
          addr:'303 4455a Greenview Dr NE'
          ptype2:['Apartment']
        },
        output:{
          cnty:'CA',
          prov:'AB',
          city:'Calgary',
          addr:'4455a Greenview Dr NE',
          unt:'303',
          st_num:'4455a',
          st:'Greenview',
          st_sfx:'Dr',
          dir:'NE',
          st_dir:'NE',
          showAddr:'4455a Greenview Dr NE'
          faddr:'4455a Greenview Dr NE, Calgary, AB, CA'
          ptype2:['Apartment']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Kimberley',
          addr:'(abcd) 1351 Gerry Sorensen Way'
        },
        output:{
          cnty: 'CA',
          prov: 'BC',
          city: 'Kimberley',
          addr: '(abcd) 1351 Gerry Sorensen Way',
          st: '(abcd) Gerry Sorensen',
          showAddr: '(abcd) 1351 Gerry Sorensen Way',
          faddr: '(abcd) 1351 Gerry Sorensen Way, Kimberley, BC, CA',
          st_sfx:'Way',
          st_num:'1351',
          sp_st_num:true
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'BC',
          city:'Kimberley',
          addr:'A 1351 Gerry Sorensen Way'
          ptype2:['Detached']
          saletp:['Lease']
        },
        output:{
          cnty:'CA',
          prov:'BC',
          city:'Kimberley',
          addr:'1351 Gerry Sorensen Way',
          unt:'A',
          st_num:'1351',
          st:'Gerry Sorensen',
          st_sfx:'Way',
          showAddr:'1351 Gerry Sorensen Way'
          faddr:'1351 Gerry Sorensen Way, Kimberley, BC, CA'
          ptype2:['Detached']
          saletp:['Lease']
        }
      },
      {
        input:{
          cnty:'CA',
          prov:'ON',
          city:'Richmond Hill',
          addr:'438 Centre St E '
          ptype2:['House','Detached','Bungalow']
          saletp:['Sale']
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Richmond Hill',
          addr: '438 Centre St E',
          ptype2: [ 'House', 'Detached', 'Bungalow' ],
          saletp: [ 'Sale' ],
          st: 'Centre',
          dir: 'E',
          st_dir:'E',
          st_sfx: 'St',
          st_num: '438',
          showAddr: '438 Centre St E',
          faddr: '438 Centre St E, Richmond Hill, ON, CA'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Toronto E05',
          addr:'911 - 100 ECHO POINT'
          ptype2:['Apartment']
          saletp:['Sale']
          unt: '911'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Toronto',
          addr: '100 Echo Pt',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          unt: '911',
          st: 'Echo',
          origUnt: '911',
          showAddr: '100 ECHO POINT',
          st_sfx: 'Pt',
          st_num: '100',
          faddr: '100 Echo Pt, Toronto, ON, CA'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Toronto Beaches',
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Toronto Beaches',
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, Toronto Beaches, ON M4L3Y8, CA'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Newmarket (Newmarket Industrial Park)',
          cmty:'Newmarket Industrial Park'
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
          src:'DDF'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Newmarket',
          cmty:'Newmarket Industrial Park'
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, Newmarket, ON M4L3Y8, CA'
          src:'DDF'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Newmarket (Newmarket Industrial Park)',
          cmty:'Newmarket Industrial Park'
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Newmarket (newmarket Industrial Park)',
          cmty:'Newmarket Industrial Park'
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, Newmarket (newmarket Industrial Park), ON M4L3Y8, CA'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Plympton-Wyoming',
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
          src:'DDF'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Plympton-Wyoming',
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, Plympton-Wyoming, ON M4L3Y8, CA'
          src:'DDF'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'Mcmurrich/Monteith',
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
          src:'DDF'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'Mcmurrich/Monteith',
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, Mcmurrich/Monteith, ON M4L3Y8, CA'
          src:'DDF'
        }
      },
      {
        input:{
          cnty:'Canada',
          prov:'Ontario',
          city:'St. John\'s',
          addr:'35 Boardwalk Dr'
          ptype2:['Apartment']
          saletp:['Sale']
          zip:'M4L 3Y8'
          unt:'Ph 1'
          src:'DDF'
        },
        output:{
          cnty: 'CA',
          prov: 'ON',
          city: 'St. John\'s',
          addr: '35 Boardwalk Dr',
          ptype2: [ 'Apartment' ],
          saletp: [ 'Sale' ],
          zip: 'M4L3Y8',
          unt: 'Ph 1',
          st: 'Boardwalk',
          origUnt: 'Ph 1',
          showAddr: '35 Boardwalk Dr',
          st_sfx: 'Dr',
          st_num: '35',
          faddr: '35 Boardwalk Dr, St. John\'s, ON M4L3Y8, CA'
          src:'DDF'
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'Alberta',
          city:'Athabasca',
          addr:'101, 202, 303, 304, 2814 48 Ave'
          zip:'T9S 0A5'
          unt:'101, 202, 303, 304'
          ptype2:['Apartment']
        }
        output:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Athabasca',
          addr: '2814 48 Ave',
          zip: 'T9S0A5',
          unt: '101 202 303 304',
          ptype2: [ 'Apartment' ],
          st: '48',
          origUnt: '101, 202, 303, 304',
          showAddr: '2814 48 Ave',
          st_sfx: 'Ave',
          st_num: '2814',
          faddr: '2814 48 Ave, Athabasca, AB T9S0A5, CA'
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'Alberta',
          city:'Strathmore',
          addr:'3 , 204 Strathaven Dr NE'
          zip:'T1P 1P6'
          unt:'3'
          ptype2:['Row/Townhouse']
        }
        output:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Strathmore',
          addr: '204 Strathaven Dr NE',
          zip: 'T1P1P6',
          unt: '3',
          ptype2: [ 'Row/Townhouse' ],
          st: 'Strathaven',
          origUnt: '3',
          showAddr: '204 Strathaven Dr NE',
          dir: 'NE',
          st_dir:'NE',
          st_sfx: 'Dr',
          st_num: '204',
          faddr: '204 Strathaven Dr NE, Strathmore, AB T1P1P6, CA'
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'Alberta',
          city:'Calgary',
          addr:'522 Building 5w, 10120 Brookpark Blvd SW'
          zip:'T1P 1P6'
          unt:'522 building 5w'
          ptype2:['Low-Rise(1-4)'],
          mfee:519
        }
        output:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Calgary',
          addr: '10120 Brookpark Blvd SW',
          zip: 'T1P1P6',
          unt: '522 Building 5w',
          ptype2: [ 'Low-Rise(1-4)' ],
          mfee: 519,
          st: 'Brookpark',
          origUnt: '522 building 5w',
          showAddr: '10120 Brookpark Blvd SW',
          dir: 'SW',
          st_dir:'SW',
          st_sfx: 'Blvd',
          st_num: '10120',
          faddr: '10120 Brookpark Blvd SW, Calgary, AB T1P1P6, CA'
        }
      },
      {
        input: {
          cnty:'CANADA',
          prov:'Alberta',
          city:'Calgary',
          addr:'1,2 1 Brookpark Blvd SW'
          zip:'T1P 1P6'
          unt:'1,2'
          ptype2:['Condo']
        },
        output:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Calgary',
          addr: '1 Brookpark Blvd SW',
          zip: 'T1P1P6',
          unt: '1 2',
          ptype2: [ 'Condo' ],
          st: 'Brookpark',
          origUnt: '1,2',
          showAddr: '1 Brookpark Blvd SW',
          dir: 'SW',
          st_dir:'SW',
          st_sfx: 'Blvd',
          st_num: '1',
          faddr: '1 Brookpark Blvd SW, Calgary, AB T1P1P6, CA'
        }
      },
      {
        input:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Athabasca',
          zip: 'T9S0A5',
          ptype2: [ 'Apartment' ],
          st: 48,
          showAddr: '2814 48 Ave',
          st_sfx: 'Ave',
          st_num: '2814',
          faddr: '2814 48 Ave, Athabasca, AB T9S0A5, CA'
        },
        output:{
          cnty: 'CA',
          prov: 'AB',
          city: 'Athabasca',
          addr: '2814 48 Ave',
          zip: 'T9S0A5',
          ptype2: [ 'Apartment' ],
          st: '48',
          showAddr: '2814 48 Ave',
          st_sfx: 'Ave',
          st_num: '2814',
          faddr: '2814 48 Ave, Athabasca, AB T9S0A5, CA'
        }
      },
      ###*
      # 边界情况测试用例
      # 测试null、undefined、空对象等边界输入情况
      ###
      {
        desc: '测试null输入 - 应该直接返回undefined'
        input: null
        output: undefined
      },
      {
        desc: '测试undefined输入 - 应该直接返回undefined'
        input: undefined
        output: undefined
      },
      {
        desc: '测试空对象输入 - 应该返回处理后的空对象'
        input: {}
        output: {
          st: undefined
          addr: undefined
          faddr: null
        }
      },
      ###*
      # 数字字段处理测试用例
      # 测试当地址字段为数字时的转换处理
      ###
      {
        desc: '测试city字段为数字 - 应该转换为字符串并发出警告'
        input: {
          city: 123
          prov: 'ON'
          cnty: 'CA'
        }
        output: {
          city: '123'
          prov: 'ON'
          cnty: 'CA'
          st: undefined
          addr: undefined
          faddr: '123, ON, CA'
        }
      },
      {
        desc: '测试prov字段为数字 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 456
          cnty: 'CA'
        }
        output: {
          city: 'Toronto'
          prov: '456'
          cnty: 'CA'
          st: undefined
          addr: undefined
          faddr: 'Toronto, 456, CA'
        }
      },
      {
        desc: '测试addr字段为数字 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: 123
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123'
          st: undefined
          showAddr: '123'
          faddr: '123, Toronto, ON, CA'
        }
      },
      {
        desc: '测试多个数字字段 - 应该全部转换为字符串'
        input: {
          city: 123
          prov: 456
          cnty: 789
          addr: 999
        }
        output: {
          city: '123'
          prov: '456'
          cnty: '789'
          addr: '999'
          st: undefined
          showAddr: '999'
          faddr: '999, 123, 456, 789'
        }
      },
      ###*
      # 字符串trim处理测试用例
      # 测试字段前后空格的处理
      ###
      {
        desc: '测试字段包含前后空格 - 应该自动trim'
        input: {
          city: '  Toronto  '
          prov: '  ON  '
          cnty: '  CA  '
          addr: '  123 Main St  '
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      ###*
      # st_dir字段处理测试用例
      # 测试街道方向的标准化处理
      ###
      {
        desc: '测试st_dir字段标准化 - NORTH应该转换为N'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_dir: 'NORTH'
          st: 'Main'
          st_num: '123'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_dir: 'N'
          st: 'Main'
          st_num: '123'
          addr: '123 Main N'
          dir: 'N'
          showAddr: '123 Main N'
          faddr: '123 Main N, Toronto, ON, CA'
        }
      },
      {
        desc: '测试st_dir字段小写输入 - north应该转换为N'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_dir: 'north'
          st: 'Main'
          st_num: '123'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_dir: 'N'
          st: 'Main'
          st_num: '123'
          addr: '123 Main N'
          dir: 'N'
          showAddr: '123 Main N'
          faddr: '123 Main N, Toronto, ON, CA'
        }
      },
      ###*
      # st_sfx字段处理测试用例
      # 测试街道后缀的标准化处理
      ###
      {
        desc: '测试st_sfx字段标准化 - STREET应该转换为St'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_sfx: 'STREET'
          st: 'Main'
          st_num: '123'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_sfx: 'St'
          st: 'Main'
          st_num: '123'
          addr: '123 Main St'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      {
        desc: '测试st_sfx字段小写输入 - avenue应该转换为Ave'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_sfx: 'avenue'
          st: 'Main'
          st_num: '123'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st_sfx: 'Ave'
          st: 'Main'
          st_num: '123'
          addr: '123 Main Ave'
          showAddr: '123 Main Ave'
          faddr: '123 Main Ave, Toronto, ON, CA'
        }
      },
      ###*
      # addr字段特殊字符处理测试用例
      # 测试addr字段中特殊字符的处理
      ###
      {
        desc: '测试addr字段包含|字符 - 应该替换为空格'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123|Main|St'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      {
        desc: '测试addr字段包含多个|字符 - 应该全部替换为空格'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123||Main|||St'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      ###*
      # zip字段处理测试用例
      # 测试邮编字段的格式化处理
      ###
      {
        desc: '测试zip字段格式化 - 小写应该转换为大写'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          zip: 'm5v3a8'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          zip: 'M5V3A8'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON M5V3A8, CA'
        }
      },
      {
        desc: '测试zip字段包含空格 - 应该移除空格'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          zip: 'M5V 3A8'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          zip: 'M5V3A8'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON M5V3A8, CA'
        }
      },
      ###*
      # unt字段处理测试用例
      # 测试单元号字段的处理
      ###
      {
        desc: '测试unt字段为0 - 应该被删除'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          unt: '0'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          unt: '0'
          st: 'Main'
          origUnt: '0'
          showAddr: '123 Main St'
          st_sfx: 'St'
          st_num: '123'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      {
        desc: '测试unt字段包含前后空格 - 应该trim'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          unt: '  201  '
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          unt: '201'
          st: 'Main'
          origUnt: '  201  '
          showAddr: '123 Main St'
          st_sfx: 'St'
          st_num: '123'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      ###*
      # 复合字段处理测试用例
      # 测试多个字段同时存在特殊情况的处理
      ###
      {
        desc: '测试st字段为数字 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st: 123
          st_num: '456'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st: '123'
          st_num: '456'
          addr: '456 123'
          showAddr: '456 123'
          faddr: '456 123, Toronto, ON, CA'
        }
      },
      ###*
      # 异常情况测试用例
      # 测试各种异常输入的处理
      ###
      {
        desc: '测试所有字段都为空字符串 - 应该正常处理'
        input: {
          city: ''
          prov: ''
          cnty: ''
          addr: ''
        }
        output: {
          city: ''
          prov: ''
          cnty: ''
          addr: undefined
          st: undefined
          faddr: null
        }
      },
      {
        desc: '测试只有部分必要字段 - 应该正常处理'
        input: {
          city: 'Toronto'
          prov: 'ON'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          st: undefined
          addr: undefined
          faddr: 'Toronto, ON, CA'
        }
      },
      ###
      # 边界情况和异常情况测试
      ###
      {
        desc: '测试空值输入 - 应该直接返回undefined'
        input: null
        output: undefined
      },
      {
        desc: '测试undefined输入 - 应该直接返回undefined'
        input: undefined
        output: undefined
      },
      {
        desc: '测试空对象输入 - 应该返回处理后的空对象'
        input: {}
        output: {
          st: undefined
          addr: undefined
          faddr: null
        }
      },
      ###
      # 数字字段处理测试 - 测试数字类型字段转换为字符串
      ###
      {
        desc: '测试city为数字类型 - 应该转换为字符串并正常处理'
        input: {
          city: 123
          prov: 'ON'
          cnty: 'CANADA'
        }
        output: {
          city: '123'
          prov: 'ON'
          cnty: 'CA'
          st: undefined
          addr: undefined
          faddr: '123, ON, CA'
        }
      },
      {
        desc: '测试prov为数字类型 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 456
          cnty: 'CANADA'
        }
        output: {
          city: 'Toronto'
          prov: '456'
          cnty: 'CA'
          st: undefined
          addr: undefined
          faddr: 'Toronto, 456, CA'
        }
      },
      {
        desc: '测试cnty为数字类型 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 789
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: '789'
          st: undefined
          addr: undefined
          faddr: 'Toronto, ON, 789'
        }
      },
      {
        desc: '测试addr为数字类型 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: 123
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123'
          st: undefined
          showAddr: '123'
          faddr: '123, Toronto, ON, CA'
        }
      },
      ###
      # Toronto特殊处理测试 - 测试Toronto C01, C02等格式的处理
      ###
      {
        desc: '测试Toronto C01格式 - 应该去掉区域代码'
        input: {
          city: 'Toronto C01'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '123 Main St'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
          st_num: '123'
          st: 'Main'
          st_sfx: 'St'
        }
      },
      {
        desc: '测试Toronto C02格式 - 应该去掉区域代码'
        input: {
          city: 'Toronto C02'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '456 Queen St W'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '456 Queen St W'
          showAddr: '456 Queen St W'
          faddr: '456 Queen St W, Toronto, ON, CA'
          st_num: '456'
          st: 'Queen'
          st_sfx: 'St'
          st_dir: 'W'
          dir: 'W'
        }
      },
      {
        desc: '测试小写toronto c07格式 - 应该去掉区域代码并标准化大小写'
        input: {
          city: 'toronto c07'
          prov: 'ontario'
          cnty: 'canada'
          addr: '789 king st e'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '789 King St E'
          st: 'King'
          st_sfx: 'St'
          st_dir: 'E'
          dir: 'E'
          st_num: '789'
          showAddr: '789 king st e'
          faddr: '789 King St E, Toronto, ON, CA'
        }
      },
      {
        desc: '测试大写TORONTO C07格式 - 应该去掉区域代码'
        input: {
          city: 'TORONTO C07'
          prov: 'ONTARIO'
          cnty: 'CANADA'
          addr: '321 BLOOR ST'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '321 Bloor St'
          st: 'Bloor'
          st_sfx: 'St'
          st_num: '321'
          showAddr: '321 BLOOR ST'
          faddr: '321 Bloor St, Toronto, ON, CA'
        }
      },
      ###
      # 不同省份和国家测试 - 测试各种省份和国家的标准化处理
      ###
      {
        desc: '测试BC省地址 - 应该正确标准化省份缩写'
        input: {
          city: 'Vancouver'
          prov: 'British Columbia'
          cnty: 'CANADA'
          addr: '1234 Robson St'
          zip: 'V6E 1B2'
        }
        output: {
          city: 'Vancouver'
          prov: 'BC'
          cnty: 'CA'
          addr: '1234 Robson St'
          zip: 'V6E1B2'
          st: 'Robson'
          st_sfx: 'St'
          st_num: '1234'
          showAddr: '1234 Robson St'
          faddr: '1234 Robson St, Vancouver, BC V6E1B2, CA'
        }
      },
      {
        desc: '测试AB省地址 - 应该正确标准化省份缩写'
        input: {
          city: 'Calgary'
          prov: 'ALBERTA'
          cnty: 'CANADA'
          addr: '567 17 Ave SW'
          zip: 'T2S 0B1'
        }
        output: {
          city: 'Calgary'
          prov: 'AB'
          cnty: 'CA'
          addr: '567 17 Ave SW'
          zip: 'T2S0B1'
          st: '17'
          st_sfx: 'Ave'
          st_dir: 'SW'
          dir: 'SW'
          st_num: '567'
          showAddr: '567 17 Ave SW'
          faddr: '567 17 Ave SW, Calgary, AB T2S0B1, CA'
        }
      },
      {
        desc: '测试美国地址 - USA应该转换为US'
        input: {
          city: 'New York'
          prov: 'NY'
          cnty: 'USA'
          addr: '123 Broadway'
          zip: '10001'
        }
        output: {
          city: 'New York'
          prov: 'NY'
          cnty: 'US'
          addr: '123 Broadway'
          showAddr: '123 Broadway'
          faddr: '123 Broadway, New York, NY 10001, US'
          zip: '10001'
          st_num: '123'
          st: 'Broadway'
        }
      },
      ###
      # 特殊字符和格式处理测试
      ###
      {
        desc: '测试地址中包含|符号 - 应该替换为空格'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '123|Main|Street'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          st: 'Main'
          st_sfx: 'St'
          st_num: '123'
          showAddr: '123 Main Street'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      {
        desc: '测试字符串前后有空格 - 应该自动trim'
        input: {
          city: '  Toronto  '
          prov: '  ON  '
          cnty: '  CANADA  '
          addr: '  123 Main St  '
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '123 Main St'
          st_num: '123'
          st: 'Main'
          st_sfx: 'St'
          showAddr: '123 Main St'
          faddr: '123 Main St, Toronto, ON, CA'
        }
      },
      ###
      # 地址构建测试 - 测试从st和st_num构建addr的情况
      ###
      {
        desc: '测试没有addr但有st和st_num - 应该自动构建addr'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          st: 'Yonge Street'
          st_num: '100'
          zip: 'M5C 2W1'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st: 'Yonge'
          st_num: '100'
          zip: 'M5C2W1'
          addr: '100 Yonge St'
          st_sfx: 'St'
          showAddr: '100 Yonge Street'
          faddr: '100 Yonge St, Toronto, ON M5C2W1, CA'
        }
      },
      {
        desc: '测试st_dir和st_sfx组合 - 应该正确处理方向和后缀'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          st: 'Queen'
          st_num: '200'
          st_dir: 'WEST'
          st_sfx: 'STREET'
          zip: 'M5V 3A8'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          st: 'Queen'
          st_num: '200'
          st_dir: 'W'
          st_sfx: 'St'
          zip: 'M5V3A8'
          addr: '200 Queen St W'
          dir: 'W'
          showAddr: '200 Queen St W'
          faddr: '200 Queen St W, Toronto, ON M5V3A8, CA'
        }
      },
      ###
      # 社区和单元号处理测试
      ###
      {
        desc: '测试包含社区信息 - 应该正确处理cmty字段'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '300 Front St W'
          cmty: 'Entertainment District'
          zip: 'M5V 3A8'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '300 Front St W'
          cmty: 'Entertainment District'
          zip: 'M5V3A8'
          st_num: '300'
          st: 'Front'
          st_sfx: 'St'
          st_dir: 'W'
          dir: 'W'
          showAddr: '300 Front St W'
          faddr: '300 Front St W, Toronto, ON M5V3A8, CA'
        }
      },
      {
        desc: '测试cmty为数字类型 - 应该记录错误日志但继续处理'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '400 Bay St'
          cmty: 12345
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '400 Bay St'
          cmty: 12345
          st_num: '400'
          st: 'Bay'
          st_sfx: 'St'
          showAddr: '400 Bay St'
          faddr: '400 Bay St, Toronto, ON, CA'
        }
      },
      {
        desc: '测试单元号处理 - 地址中包含单元号格式'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '#501-500 University Ave'
          zip: 'M5G 1V7'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '#501-500 University Ave'
          zip: 'M5G1V7'
          st: undefined
          st_sfx: 'Ave'
          showAddr: '#501-500 University Ave'
          faddr: '#501-500 University Ave, Toronto, ON M5G1V7, CA'
        }
      },
      {
        desc: '测试unt字段为数字 - 应该转换为字符串'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '600 King St W'
          unt: 1001
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '600 King St W'
          unt: '1001'
          st_num: '600'
          st: 'King'
          st_sfx: 'St'
          st_dir: 'W'
          dir: 'W'
          origUnt: '1001'
          showAddr: '600 King St W'
          faddr: '600 King St W, Toronto, ON, CA'
        }
      },
      ###
      # 邮编处理测试
      ###
      {
        desc: '测试邮编格式化 - 应该去掉空格并大写'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '700 Adelaide St W'
          zip: 'm5v 2z5'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '700 Adelaide St W'
          zip: 'M5V2Z5'
          st_num: '700'
          st: 'Adelaide'
          st_sfx: 'St'
          st_dir: 'W'
          dir: 'W'
          showAddr: '700 Adelaide St W'
          faddr: '700 Adelaide St W, Toronto, ON M5V2Z5, CA'
        }
      },
      {
        desc: '测试无效邮编 - 无效邮编会被保留'
        input: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CANADA'
          addr: '800 Richmond St W'
          zip: 'invalid'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '800 Richmond St W'
          zip: 'INVALID'
          st_num: '800'
          st: 'Richmond'
          st_sfx: 'St'
          st_dir: 'W'
          dir: 'W'
          showAddr: '800 Richmond St W'
          faddr: '800 Richmond St W, Toronto, ON INVALID, CA'
        }
      },
      ###
      # 复杂组合测试 - 测试多个功能组合的情况
      ###
      {
        desc: '测试复杂地址组合 - 包含所有主要字段'
        input: {
          city: 'TORONTO C08'
          prov: 'ONTARIO'
          cnty: 'CANADA'
          addr: '#PH01-900 BAY STREET WEST'
          cmty: 'Financial District'
          zip: 'm5s 3a3'
          st_dir: 'WEST'
          st_sfx: 'STREET'
        }
        output: {
          city: 'Toronto'
          prov: 'ON'
          cnty: 'CA'
          addr: '#ph01-900 Bay St W'
          cmty: 'Financial District'
          zip: 'M5S3A3'
          st_dir: 'W'
          st_sfx: 'St'
          st: undefined
          dir: 'W'
          showAddr: '#PH01-900 BAY STREET WEST'
          faddr: '#ph01-900 Bay St W, Toronto, ON M5S3A3, CA'
        }
      },
      {
        desc: '测试空字符串字段 - 应该正确处理空字符串'
        input: {
          city: ''
          prov: 'ON'
          cnty: 'CANADA'
          addr: ''
        }
        output: {
          city: ''
          prov: 'ON'
          cnty: 'CA'
          addr: undefined
          st: undefined
          faddr: null
        }
      }
    ]
    tests.forEach (test)->
      it test.desc or 'should format addr object', (done)->
        formatedObj = propAddress.formatProvAndCity(test.input)
        # console.log 'formatedObj',formatedObj
        if test.output is undefined
          should(formatedObj).be.undefined()
        else
          should.deepEqual(formatedObj,test.output)
        done()

    #correctCmty
  describe 'correctCmty ', ->
    tests = [
      {
        input: 'Om Old Milton',
        output:'Old Milton'
      },
      {
        input: '0120 - Clarkson',
        output:'Clarkson'
      },
      {
        input: 'Wi Willmot',
        output:'Willmot'
      },
      {
        input:'Cl Clarke',
        output:'Clarke',
      },
      {
        input:'Fd Ford',
        output:'Ford'
      },
      {
        input: 'CV CLEARVIEW',
        output:'Clearview'
      },
      {
        input: 'Wt West Oak Trails',
        output:'West Oak Trails'
      },
      {
        input:'Or61-Hawkestone',
        output:'Hawkestone'
      },
      {
        input:'Se54-Washago',
        output:'Washago'
      },
      {
        input:'Ra40-Rural Ramara',
        output:'Rural Ramara'
      },
      {
        input:'Ra40 Rural Ramara',
        output:'Rural Ramara'
      },
      {
        input:'ZZ-OL01-ORILLIA',
        output:'Orillia'
      },
      {
        input:'Emerald Meadows, Trailwest',
        output:'Emerald Meadows'
      },
      {
        input:'BOBCAYGEON (TOWN)',
        output:'Bobcaygeon'

      },
      {
        input:  'Mt Albert',
        output: 'Mt Albert'
      },
      {
        input: 'N/A',
        output: null
      },
      {
        input:'Waterfront Communities C1',
        output: 'Waterfront Communities C01',
      },
      {
        input:'Su Lower Town',
        output:'Su Lower Town'
      },
      {
        input:'Mt Tolmie',
        output:'Mt Tolmie'
      },
      {
        input:'Su Main Town',
        output:'Su Main Town'
      },
      {
        input:'Pe Main South',
        output:'Pe Main South'
      },
      {
        input:'Or62 - Rural Oro-Medonte',
        output:'Rural Oro-Medonte'
      },
      {
        input:'Se Hill',
        output:'Se Hill'
      },
      {
        input:'Ko Kaleden',
        output:'Ko Kaleden'
      },
      {
        input:'Fletcher\'s Creek South',
        output:'Fletcher\'s Creek South'
      },
      {
        input:'L\'Amoreaux',
        output:'L\'Amoreaux'
      },
      {
        input:'10 Denman Island (zone 2)',
        output:'Denman Island'
      },
      {
        input:'10 Quadra Island (zone 1)'
        output:'Quadra Island'
      },
      {
        input:'O\'Shanter-Sullivan',
        output:'O\'Shanter-Sullivan'
      },
      {
        input:'St. John\'s',
        output:'St. John\'s'
      },
      {
        input:'EMPIRE SOUTH.'
        output:'Empire South'
      },
      {
        input:'HOLMEDALE/',
        output:'Holmedale'
      },
      {
        input:'Burk\'S Falls',
        output:'Burk\'s Falls'
      },
      {
        input:'TWMC-MIMICO',
        output:'Mimico'
      },
      {
        input:'Armstrong/ Spall.'
        output:'Armstrong/Spall'
      },
      {
        input:'Wm Westmount',
        output:'Westmount'
      },
      {
        input:'Tcw1-Waterfront Communities C1'
        output:'Waterfront Communities C01'
      },
      {
        input:'Tcmw-Mount Pleasant West',
        output:'Mount Pleasant West'
      },
      {
        input:'Tcw8-Waterfront Communities C8',
        output:'Waterfront Communities C08'
      },
      {
        input:'Twhp-High Park-Swansea',
        output:'High Park-Swansea'
      },
      {
        input:'Twma-Markland Wood',
        output:'Markland Wood'
      },
      {
        input:'Tcwe-Willowdale East',
        output:'Willowdale East'
      },
      {
        input:'Tcni-Niagara',
        output:'Niagara'
      },
      {
        input:'Tcbc-Bay Street Corridor',
        output:'Bay Street Corridor'
      },
      {
        input:'Tesr-South Riverdale',
        output:'South Riverdale',
      },
      {
        input:'Tcsw-St. Andrew-Windfields',
        output:'St. Andrew-Windfields'
      },
      {
        input:'Tccy-Church-Yonge Corridor',
        output:'Church-Yonge Corridor'
      }
    ]
    tests.forEach (test)->
      it "should correctCmty #{test.input}", (done)->
        formatedObj = propAddress.correctCmty(test.input)
        should.deepEqual(test.output,formatedObj)
        done()

  describe 'getFullAddress ', ->
    tests = [
      {
        desc:'Standard format input,should add zip to qaddr'
        input:{
          cnty:'CA'
          prov:'ON'
          city:'Toronto'
          addr:'38 William St'
          zip:'M9N2G7'
        }
        appendZip:true
        expected:'38 William St, Toronto, ON M9N2G7, CA'
      },
      {
        desc:'Standard format input,should not add zip to qaddr, and province should be abbreviation'
        input:{
          cnty:'CA'
          prov:'Ontario'
          city:'Toronto'
          addr:'38 William St'
          zip:'M9N2G7'
        }
        appendZip:false
        expected:'38 William St, Toronto, ON, CA'
      },
      {
        desc:'no country, should add default country(CA)'
        input:{
          prov:'Ontario'
          city:'Toronto'
          addr:'38 William St'
          zip:'M9N2G7'
        }
        appendZip:true
        expected:'38 William St, Toronto, ON M9N2G7, CA'
      },
      {
        desc:'no province, should return null'
        input:{
          cnty:'CA'
          city:'Toronto'
          addr:'38 William St'
          zip:'M9N2G7'
        }
        appendZip:false
        expected:null
      },
      {
        desc:'no city, should return null'
        input:{
          cnty:'CA'
          prov:'ON'
          addr:'38 William St'
          zip:'M9N2G7'
        }
        appendZip:true
        expected:null
      },
      {
        desc:'no addr, addr should compose of st and st_num '
        input:{
          cnty:'CA'
          prov:'ON'
          city:'Toronto'
          st:'William St'
          st_num:'38'
          zip:'M9N2G7'
        }
        appendZip:false
        expected:'38 William St, Toronto, ON, CA'
      },
      {
        desc:'no addr and st_num, addr should be st'
        input:{
          cnty:'CA'
          prov:'ON'
          city:'Toronto'
          st:'William St'
          zip:'M9N2G7'
        }
        appendZip:false
        expected:'William St, Toronto, ON, CA'
      },
      {
        desc:'no addr,st and st_num, addr should be null'
        input:{
          cnty:'CA'
          prov:'ON'
          city:'Toronto'
          zip:'M9N2G7'
        }
        appendZip:false
        expected:'Toronto, ON, CA'
      },
      {
        desc:'should add cmty in qaddr'
        input:{
          cnty:'CA'
          prov:'Ontario'
          city:'Toronto'
          addr:'120 Homewood Ave'
          cmty:'Newtonbrook West'
          zip:'M2M1K3'
        }
        appendZip:true
        appendCmty:true
        expected:'120 Homewood Ave, Newtonbrook West, Toronto, ON M2M1K3, CA'
      },
      {
        desc:'should add zip in qaddr'
        input:{
          cnty:'CA'
          prov:'Ontario'
          city:'Toronto'
          addr:'120 Homewood Ave'
          zip:'M2M1K3'
        }
        appendZip:true
        expected:'120 Homewood Ave, Toronto, ON M2M1K3, CA'
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        qaddr = propAddress.getFullAddress(test.input,test.appendZip,test.appendCmty)
        should.deepEqual(qaddr,test.expected)
        done()

  describe 'formatAddr',->
    tests = [
      {
        desc:'should remove Drive from addr'
        input:'3985 Grand Park Drive Dr N'
        output:'3985 Grand Park Dr N'
      },
      {
        desc:'Circ should be Cir'
        input:'3438 Enniskillen Circ'
        output:'3438 Enniskillen Cir'
      },
      {
        desc:'should remove Avenue from addr'
        input:'153 Cave Avenue Ave'
        output:'153 Cave Ave'
      },
      {
        desc:'should remove Trail from addr'
        input:'10 Moreau Trail Tr'
        output:'10 Moreau Tr'
      },
      {
        desc:'Trail should be Tr'
        input:'10 Moreau Trail'
        output:'10 Moreau Tr'
      },
      {
        desc:'Trails should be Tr'
        input:'10 Moreau Trails'
        output:'10 Moreau Tr'
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        formatedAddr = propAddress.formatAddr(test.input)
        should.deepEqual(test.output,formatedAddr)
        done()

  describe 'decodeFromUaddr', ->
    tests = [
      {
        desc:'should return cnty,prov,city,addr'
        input:'CA:ON:TORONTO:38 WILLIAM ST'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'38 William St'
        }
      },
      {
        desc:'should return cnty,prov,city,addr,zip3'
        input:'CA:ON:TORONTO:M9N:38 WILLIAM ST'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'38 William St'
          zip3:'M9N'
        }
      },
      {
        desc:'should return undefined for no uaddr'
        input:''
      },
      {
        desc:'should return undefined for no addr'
        input:'CA:ON:TORONTO'
      },
      {
        desc:'should return undefined for no addr/city'
        input:'CA:BC'
      },
      {
        desc:'should return undefined for no addr'
        input:'CA:ON:TAY'
      },
      {
        desc:'should return cnty,prov,city,addr'
        input:'CA:ON:DWIGHT:7'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Dwight'
          addr:'7'
        }
      },
      {
        desc:'should return cnty,prov,city,addr'
        input:'CA:ON:OTTAWA:1'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Ottawa'
          addr:'1'
        }
      },
      {
        desc:'should return cnty,prov,city,addr'
        input:'CA:ON:MARKHAM:MARKHAM DENTIST | CACHET WOODS DENTAL CLEANING | WISDOM TEETH REMOVAL | 万锦牙医诊所 | 洗牙 | TEETH WHITENING WOODBINE AVE'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Markham'
          addr:'Markham Dentist | Cachet Woods Dental Cleaning | Wisdom Teeth Removal | 万锦牙医诊所 | 洗牙 | Teeth Whitening Woodbine Ave'
        }
      },
      {
        desc:'should return cnty,prov,city,addr'
        input:'CA:ON:TORONTO:B-STRETCHED CHURCH AND CHARLES - PHYSIOTHERAPISTS CHIROPRACTORS ACUPUNCTURE MASSAGE EXERCISE CHARLES ST E'
        expected:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'B-Stretched Church And Charles - Physiotherapists Chiropractors Acupuncture Massage Exercise Charles St E'
        }
      },
      {
        desc:'should return cnty,prov,city,addr when more than four parts'
        input:'CA:AB:BEAVERLODGE:820:840 8 AVE'
        expected:{
          cnty:'CA'
          prov:'Ab'
          city:'Beaverlodge'
          addr:'820:840 8 Ave'
        }
      },
      {
        desc:'should return cnty,prov,city,addr when more than four parts'
        input:'CA:AB:CALGARY:W:5 R:1 T: 26 S:21 Q:NORT TOWNSHIP ROAD 264 RANGE RD 13'
        expected:{
          cnty:'CA'
          prov:'Ab'
          city:'Calgary'
          addr:'W:5 R:1 T: 26 S:21 Q:nort Township Road 264 Range Rd 13'
        }
      },
      {
        desc:'should return cnty,prov,city,addr when more than four parts'
        input:'CA:SK:RADISSON:20 B:18 GEORGE ST'
        expected:{
          cnty:'CA'
          prov:'Sk'
          city:'Radisson'
          addr:'20 B:18 George St'
        }
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        ret = propAddress.decodeFromUaddr(test.input)
        should.deepEqual(ret,test.expected)
        done()
  
  describe 'unifyAddress',->
    tests = [
      {
        desc:'should return null for noAddr',
        input:{noAddr:true}
      },
      {
        desc:'should return null for no prov',
        input:{cnty:'CA',city:'TORONTO',addr:'38 William St'}
      },
      {
        desc:'should return uaddr',
        input:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'120 Homewood Ave'
          zip:'M2M1K3'
        }
        expected:'CA:ON:TORONTO:120 HOMEWOOD AVE'
      },
      {
        desc:'uaddr should not add zip for no zip',
        input:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'120 Homewood Ave'
        }
        appendZip:true
        expected:'CA:ON:TORONTO:120 HOMEWOOD AVE'
      },
      {
        desc:'uaddr should add zip',
        input:{
          cnty:'CA'
          prov:'On'
          city:'Toronto'
          addr:'120 Homewood Ave'
          zip:'M2M1K3'
        }
        appendZip:true
        expected:'CA:ON:TORONTO:M2M:120 HOMEWOOD AVE'
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        ret = unifyAddress test.input,test.appendZip
        if test.expected
          ret.should.be.exactly(test.expected)
        else
          should.not.exists(ret)
        done()

  describe 'checkPropCity',->
    tests = [
      {
        desc:'test has city no prov'
        input:{
          city:'Toronto'
          prov:''
          cmty:''
          src:''
        }
        expected:'Toronto'
      },
      {
        desc:'test has prov no city'
        input:{
          city:''
          prov:'ON'
          cmty:''
          src:''
        }
        expected:''
      },
      {
        desc:'test normal city'
        input:{
          city:'Toronto'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Toronto'
      },
      {
        desc:'test sub city'
        input:{
          city:'North York'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Toronto',
        subCity:'North York'
      },
      {
        desc:'test normal city with areacode'
        input:{
          city:'Toronto C07'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Toronto'
      },
      {
        desc:'test city with number, will has warning log'
        input:{
          city:'Toronto 1'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Toronto 1'
      },
      {
        desc:'test ddf city has cmty'
        input:{
          city:'Newmarket (Newmarket Industrial Park)'
          prov:'ON'
          cmty:'Newmarket Industrial Park'
          src:'DDF'
        }
        expected:'Newmarket'
      },
      {
        desc:'test not ddf city has cmty'
        input:{
          city:'Newmarket (Newmarket Industrial Park)'
          prov:'ON'
          cmty:'Newmarket Industrial Park'
          src:'TRB'
        }
        expected:'Newmarket (newmarket Industrial Park)'
      },
      {
        desc:'test normal city with char of -'
        input:{
          city:'Plympton-Wyoming'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Plympton-Wyoming'
      },
      {
        desc:'test normal city with char of /'
        input:{
          city:'Mcmurrich/Monteith'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'Mcmurrich/Monteith'
      },
      {
        desc:'test normal city with char of .\''
        input:{
          city:'St. John\'s'
          prov:'ON'
          cmty:'Hillcrest Village'
          src:''
        }
        expected:'St. John\'s'
      },
      {
        desc:'test subCity for London West'
        input:{
          city:'London West'
          prov:'ON'
          cmty:''
          src:'TRB'
        }
        expected:'London',
        subCity:'London West'
      },
      {
        desc:'test subCity for London North'
        input:{
          city:'London North'
          prov:'ON'
          cmty:''
          src:'TRB'
        }
        expected:'London',
        subCity:'London North'
      },
      {
        desc:'test subCity for London East'
        input:{
          city:'London East'
          prov:'ON'
          cmty:''
          src:'TRB'
        }
        expected:'London',
        subCity:'London East'
      },
      {
        desc:'test no subCity for London'
        input:{
          city:'London'
          prov:'ON'
          cmty:''
          src:'TRB'
        }
        expected:'London'
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        srcInput = Object.assign {},test.input
        ret = propAddress.checkPropCity test.input
        ret.should.be.exactly(test.expected)
        if test.subCity
          srcInput.subCity = test.subCity
        should.deepEqual(srcInput,test.input)
        done()

  describe 'test abbrExposure', ->
    tests = [
      {
        desc: '测试空值输入'
        input: null
        expected: undefined
      },
      {
        desc: '测试空字符串输入 - 旧测试'
        input: ''
        expected: undefined
      },
      {
        desc: '测试单个方向字符串 - 带空格'
        input: 'NORTH WEST'
        expected: ['NW']
      },
      {
        desc: '测试单个方向字符串 - 不带空格'
        input: 'NORTHWEST'
        expected: ['NW']
      },
      {
        desc: '测试单个方向字符串 - 带点号'
        input: 'N.W'
        expected: ['NW']
      },
      {
        desc: '测试多个方向字符串 - 逗号分隔'
        input: 'NORTH,SOUTH,EAST,WEST'
        expected: ['N', 'S', 'E', 'W']
      },
      {
        desc: '测试数组输入 - 标准方向'
        input: ['NORTH', 'SOUTH']
        expected: ['N', 'S']
      },
      {
        desc: '测试数组输入 - 混合格式'
        input: ['N.W', 'SOUTHEAST', 'W']
        expected: ['NW', 'SE', 'W']
      },
      {
        desc: '测试小写输入'
        input: 'north west'
        expected: ['NW']
      },
      {
        desc: '测试未知方向 - 保留原值'
        input: 'UNKNOWN'
        expected: ['UNKNOWN']
      },
      {
        desc: '测试数组中包含空值'
        input: ['NORTH', null, 'SOUTH']
        expected: ['N', 'S']
      },
      {
        desc: '测试数字类型输入'
        input: 123
        expected: undefined
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        ret = propAddress.abbrExposure test.input
        if test.expected is undefined
          should(ret).be.undefined()
        else
          ret.should.be.eql(test.expected)
        done()

  describe 'test extractAddressFromOrigAddr', ->
    tests = [
      {
        desc: '测试包含city/prov信息的地址 - case 1'
        input: '#1814 - 292 Verdale Crossing, Markham, ON L6G 0H6'
        expected: '#1814 - 292 Verdale Crossing'
      },
      {
        desc: '测试包含city/prov信息的地址 - case 2'
        input: '511, 128 2 Street SW'
        expected: '128 2 Street SW'
      },
      {
        desc: '测试普通地址不包含逗号'
        input: '123 Main Street'
        expected: '123 Main Street'
      },
      {
        desc: '测试空值输入'
        input: null
        expected: null
      },
      {
        desc: '测试undefined输入'
        input: undefined
        expected: undefined
      },
      {
        desc: '测试非字符串输入'
        input: 123
        expected: 123
      },
      {
        desc: '测试空字符串输入'
        input: ''
        expected: ''
      },
      {
        desc: '测试包含Unit前缀的地址'
        input: 'Unit 1, 123 Main St, Toronto, ON'
        expected: '123 Main St'
      },
      {
        desc: '测试第一部分是纯unit的情况'
        input: '#501, 123 King Street, Toronto, ON'
        expected: '123 King Street'
      },
      {
        desc: '测试第一部分是复杂unit的情况'
        input: 'PH-12, 456 Queen Street, Vancouver, BC'
        expected: '456 Queen Street'
      },
      {
        desc: '测试第一部分不是unit的情况'
        input: '123 Main Street, Toronto, ON'
        expected: '123 Main Street'
      },
      {
        desc: '测试包含多个逗号的复杂地址'
        input: '#1814 - 292 Verdale Crossing, Markham, ON L6G 0H6, Canada'
        expected: '#1814 - 292 Verdale Crossing'
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        ret = propAddress.extractAddressFromOrigAddr test.input
        if (test.expected is null) or (test.expected is undefined)
          should.not.exists(ret)
        else
          ret.should.be.exactly(test.expected)
        done()