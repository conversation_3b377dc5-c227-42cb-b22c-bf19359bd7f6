###
Description:    修复房源showAddr字段问题，重新计算并更新showAddr字段
Usage:          ./start.sh -n fix_showAddr -cmd "lib/batchBase.coffee batch/prop/fix_showAddr.coffee dryrun"
Create date:    2025-08-14
Author:         <PERSON><PERSON><PERSON>
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{separateUnitFromAddr,checkIsRunFuncSeparateUnitFromAddr,extractAddressFromOrigAddr} = INCLUDE 'lib.propAddress'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 查询有地址的非RM房源
  query = {
    addr: {$exists: true, $nin: [null, '']},
    src: {$ne: 'RM'}
  }
  
  projection = {
    addr: 1
    unt: 1
    ptype2: 1
    ptype2_en: 1
    saletp: 1
    mfee: 1
    showAddr: 1
    origAddr: 1
  }
  
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # 计算新的showAddr
      newShowAddr = null
      if checkIsRunFuncSeparateUnitFromAddr(prop)
        # 需要调用separateUnitFromAddr
        result = separateUnitFromAddr(prop.addr, prop.unt)
        newShowAddr = result.showAddr
      else
        # 不需要调用separateUnitFromAddr，直接使用addr
        newShowAddr = prop.addr

      # 检查是否需要更新
      if newShowAddr is prop.showAddr
        speedMeter.check { noChange: 1 }
        return cb()

      update = {noModifyMt: true, $set: {showAddr: newShowAddr}}

      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, " +
          "old:#{prop.showAddr}, new:#{newShowAddr}"
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()
