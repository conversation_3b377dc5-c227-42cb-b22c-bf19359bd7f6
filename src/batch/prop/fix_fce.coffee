###
Description:    修复房源fce字段问题,特别处理BRE房源的BCRES_DirectionFacesRearYard字段
Usage:          ./start.sh -n fix_fce -cmd "lib/batchBase.coffee batch/prop/fix_fce.coffee dryrun"
Create date:    2025-03-27
Author:         xiaoweiLuo
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{abbrExposure} = INCLUDE 'lib.propAddress'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 查询非RM房源
  query = {
    src:{$ne:'RM'}
  }
  projection = {
    FrontageType:1, # CAR
    UNITEXPOSURE:1, # EDM
    UnitExposure:1, # CLG
    YardFaces:1,    # CLG
    BCRES_DirectionFacesRearYard: 1 # BRE
    fce: 1
  }
  
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      update = {noModifyMt: true,$set:{}}

      if prop.fce
        if newFce = abbrExposure(prop.fce)
          update.$set.fce = newFce
        else
          debug.warn "propId: #{prop._id} fce is not expected",prop
          speedMeter.check { notExpectedFce: 1 }
          return cb()
      else if prop.BCRES_DirectionFacesRearYard and (newFce = abbrExposure(prop.BCRES_DirectionFacesRearYard))
        update.$set.fce = newFce
      else if prop.FrontageType?.length > 0 and (newFce = abbrExposure(prop.FrontageType))
        update.$set.fce = newFce
      else if prop.UNITEXPOSURE?.length > 0 and (newFce = abbrExposure(prop.UNITEXPOSURE))
        update.$set.fce = newFce
      else if prop.UnitExposure?.length > 0 and (newFce = abbrExposure(prop.UnitExposure))
        update.$set.fce = newFce
      else if prop.YardFaces and (newFce = abbrExposure(prop.YardFaces))
        update.$set.fce = newFce
      else
        debug.debug "propId: #{prop._id} not found fce or not expected fce",prop
        speedMeter.check { noFce: 1 }
        return cb()
        
      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, update:",update
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()