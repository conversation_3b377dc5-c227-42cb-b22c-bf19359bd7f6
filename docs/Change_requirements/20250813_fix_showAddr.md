# 需求 [fix_showAddr]

## 反馈

1. 前端房源的地址应该显示源数据，而不是显示format之后的地址

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON> xiaowei

## 提出日期:      2025-08-13

## 原因

1. 现在展示的地址为format之后的信息。

## 解决办法

1. 修改`showAddr`的生成逻辑
  1. 如果房源本身存在`origAddr`字段并且没有`addr`字段,需要通过`origAddr`字段来得到`addr`信息。eg: `#S1 - 11 Ellington Drive, Toronto, ON M1R 3X6` -> `11 Ellington Drive`
  2. 如果原始地址没有unit信息,使用源数据的`addr`作为`showAddr`
  3. 如果原始地址包含unit信息,使用去掉unit信息之后的`addr`作为`showAddr`
2. 前端地址展示优先使用`showAddr`字段
3. 添加batch修复历史数据

## 是否需要补充UT

1. 需要修复并补充UT

## 确认日期:    2025-08-13

## online-step

1. 重启watch&import
2. 执行batch修复历史数据的`showAddr`
```
```
3. 重启server